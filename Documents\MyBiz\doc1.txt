# MyBiz Multi-Tenant E-Commerce Platform - AI Website Builder Prompt

## Project Overview
Create a comprehensive multi-tenant e-commerce platform called **MyBiz** targeting Tanzanian users. The platform connects local businesses with customers through a centralized marketplace, voucher system, and **robust business directory with job posting capabilities**.

## Core Functionality Requirements

### 1. Multi-Tenant Architecture
- Support for unlimited business partners with unique subdomains (format: `partnername.mybiz.co.tz`)
- Separate dashboards for admin, business partners, and customers
- Scalable infrastructure to handle multiple concurrent stores

### 2. User Types & Authentication
- **Admin**: Full platform control and management
- **Business Partners**: Store owners with subscription plans
- **Customers**: End users who browse and purchase

### 3. Subscription Plans

**Business Partner Plans:**
- Silver: TSH 20,000/month (1-page website)
- Gold: TSH 30,000/month (2-page website)
- Tanzanite: TSH 50,000/month (multiple pages website)
- **Marketing Add-on**: TSH 10,000/month (optional for all plans)

**Customer Plans:**
- Standard: TSH 1,500 (5 vouchers up to 20% off + 2 buy-one-get-one-free offers)
- Silver: TSH 3,000 (15 vouchers 20% off and above + 5 buy-one-get-one-free offers)
- Gold: TSH 7,500 (10% off vouchers and above + free vouchers + 10 buy-one-get-one-free offers)

### 4. Payment System
- **No direct payment processing**
- Generate unique codes for each purchase
- Send codes via email and WhatsApp with business details (including bank account/mobile money payment details)
- **Order tracking**: Generated codes and order details sent simultaneously to customer, business partner, and admin dashboard
- Track payment status manually through admin dashboard
- Business partners receive instant notifications for new orders with customer and payment information

### 5. Business Directory & Job Portal
- **Comprehensive business directory** with advanced search functionality
- **Business categories**: Services, retail, hospitality, healthcare, technology, etc.
- **Advanced search filters**: Location, category, rating, services offered, business size
- **Business profiles**: Detailed company information, contact details, photos, reviews
- **Job posting system**: 
  - Partners can post job vacancies directly from their dashboard
  - Job categories and requirements specification
  - Application management system
  - Job expiry and renewal options
- **Public job board**: Accessible to all users (registered and non-registered)
- **Job search functionality**: Filter by location, category, salary range, experience level

### 6. Voucher System
- Partners can create and manage vouchers (free to 10%+ off)
- **Enhanced voucher features**:
  - Terms & Conditions (T&Cs) with detailed usage rules
  - Expiry dates with countdown timers
  - Savings amount display (exact TSH amount saved)
  - Usage limitations and restrictions
  - Minimum purchase requirements
  - Valid locations/branches
- **Icon-based information display**: Hover/click icons to reveal detailed information without cluttering the interface
- Customers can browse, save to wishlist, and redeem vouchers
- Voucher categories and trending displays
- Integration with customer subscription benefits

## Required Pages & Features

### Homepage
- **Header with integrated business directory search**:
  - Business directory search icon in header
  - When clicked, reveals hidden search bar with quick category filters
  - Search functionality accessible from all pages
- **Hero section styled like modern e-commerce platforms**:
  - Large product showcase with vibrant gradient background
  - Featured product/service with pricing
  - Call-to-action buttons
  - Product category navigation icons below hero
  - Modern card-based layout similar to electronics e-commerce sites
- **Voucher showcase section** with enhanced details (T&Cs, expiry, savings amount)
- **Trending products display** with modern card layout
- **About section** (moved to under trending products display)
- **Trending shops/websites section**: Card/grid layout with:
  - Partner business ratings (star system)
  - Brief business description
  - Click-to-visit partner website functionality
  - Icon-based information display (hover/click for details)
- Trending categories
- Business owner signup section with benefits
- Customer subscription plans comparison
- **Background animations**: Scroll-triggered animations throughout the entire website

### Store/Marketplace Page
- Product/service listings from all partners
- Search and filter functionality
- Category browsing
- Partner store highlights

### All Shops Directory
- **Featured jobs section** (moved from homepage)
- **Card/grid layout** for partner business display
- **Enhanced business cards featuring**:
  - Business name and logo
  - Star rating system with review count
  - Brief business description (2-3 lines)
  - Business category tags
  - Click-to-visit partner website (partnername.mybiz.co.tz)
  - Icon-based information system:
    - Location icon (hover for address)
    - Phone icon (hover for contact details)
    - Info icon (hover for business hours)
    - Star icon (click for detailed reviews)
    - Job icon (shows if business has active job postings)
- Category-based filtering with visual filters
- Search functionality with auto-suggestions
- Sorting options (rating, newest, alphabetical)
- **Advanced filters**: Location, services offered, business size, hiring status

### Jobs Portal Page
- **Public job board** accessible to all users
- **Job search functionality** with filters:
  - Location (region, city, area)
  - Category (technology, healthcare, retail, etc.)
  - Salary range
  - Experience level
  - Job type (full-time, part-time, contract)
  - Company size
- **Job listing cards** with:
  - Company name and logo
  - Job title and brief description
  - Salary range (if provided)
  - Location and job type
  - Application deadline
  - Company rating
- **Job application system** for non-partners
- Information for potential partners
- Benefits of joining MyBiz
- Subscription plan details
- Success stories and testimonials

### Admin Dashboard
- Partner management (approve, pause, deactivate)
- User management and tracking
- **Job posting moderation**: Approve/reject job postings, monitor compliance
- **Business directory management**: Verify business information, handle disputes
- Subscription tracking and payment monitoring
- Messaging system (individual/broadcast to users/partners)
- Analytics and reporting (e-commerce, directory, jobs)
- Notification management

### Business Partner Dashboard
- Website/store management interface
- Product/service catalog management
- **Job posting management**:
  - Create and post job vacancies
  - Set job requirements and qualifications
  - Manage job applications and applicant tracking
  - Schedule interviews and communicate with candidates
  - Job posting analytics and performance tracking
- Order and inquiry management
- Message/notification center
- Voucher creation and management
- **Business directory profile management**:
  - Update business information and photos
  - Manage business categories and services
  - Respond to customer reviews and ratings
- Analytics for their store and job postings
- Subscription and payment status

### Customer Dashboard
- Voucher collection and wishlist
- Product/service wishlist
- Favorite businesses
- **Job applications tracking**: Applied jobs, application status, interview schedules
- **Business directory favorites**: Save frequently contacted businesses
- Purchase history and tracking
- Subscription management
- Notification preferences

## Technical Requirements

### Design & UX Features
- **Theme Switcher**: Light and dark mode toggle
- **Language Switcher**: English and Swahili support
- **Animated Background**: Scroll-triggered animations throughout the entire website
- **Back to Top Button**: Smooth scrolling functionality
- **Logo Navigation**: Clicking logo returns to homepage top
- **Icon-based Information System**: 
  - Hover/click icons for detailed information
  - Reduces visual clutter while maintaining information accessibility
  - Consistent iconography across vouchers and business cards
- **Card/Grid Layouts**: Modern, mobile-responsive design for business listings
- **Responsive Design**: Mobile-first approach for Tanzanian users
- **Modern Hero Section**: E-commerce style with product showcase, gradient backgrounds, and category navigation

### Performance & Scalability
- Fast loading times (critical for Tanzanian internet infrastructure)
- Optimized images and assets
- Progressive Web App (PWA) capabilities
- Offline functionality where possible

### Communication Features
- WhatsApp integration for order notifications
- Email system for order confirmations and vouchers
- In-platform messaging between users and partners
- Notification system for subscriptions and updates

## Localization Requirements
- **Currency**: Display all prices in Tanzanian Shillings (TSH)
- **Language**: Bilingual support (English/Swahili)
- **Cultural Context**: Design elements that resonate with Tanzanian users
- **Mobile-First**: Optimized for mobile usage patterns in Tanzania

## Business Logic
- 48-hour website creation workflow for new partners
- Automated subscription tracking and renewal reminders
- Voucher redemption tracking and analytics
- Partner performance metrics and reporting
- Customer engagement analytics

## Security & Data Protection
- Secure user authentication and authorization
- Data encryption for sensitive information
- Regular backup systems
- Compliance with local data protection regulations

## Integration Requirements
- WhatsApp Business API for notifications
- Email service provider integration
- SMS gateway for additional notifications
- Social media integration for marketing

## Success Metrics to Track
- Partner acquisition and retention rates
- Customer subscription conversion rates
- Voucher redemption rates
- Platform transaction volume
- User engagement metrics
- Revenue per partner/customer

## Technical Implementation Requirements
- **Development Stack**: Website must be created with HTML and JavaScript
- **Icon System**: Make use of professional icons throughout the platform (FontAwesome, Lucide, or similar professional icon libraries)
- **Code Quality**: Clean, maintainable code with proper commenting and documentation

## Development Priorities
1. Core multi-tenant architecture
2. User authentication and dashboard systems
3. Voucher management system
4. Partner onboarding workflow
5. Customer-facing marketplace
6. Payment tracking and notification system
7. Analytics and reporting features
8. Mobile optimization and PWA features

Create a modern, user-friendly platform that serves as Tanzania's premier multi-tenant e-commerce solution, emphasizing ease of use, local relevance, and sustainable business growth for all stakeholders.
