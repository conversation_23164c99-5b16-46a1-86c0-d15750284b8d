/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-color-alt: #1d4ed8;
  --secondary-color: #f59e0b;
  --accent-color: #10b981;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  
  /* Text Colors */
  --text-color: #1f2937;
  --text-color-light: #6b7280;
  --text-color-lighter: #9ca3af;
  
  /* Background Colors */
  --body-color: #ffffff;
  --container-color: #f9fafb;
  --card-color: #ffffff;
  --border-color: #e5e7eb;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  
  /* Typography */
  --body-font: 'Inter', sans-serif;
  --h1-font-size: 2.5rem;
  --h2-font-size: 2rem;
  --h3-font-size: 1.5rem;
  --normal-font-size: 1rem;
  --small-font-size: 0.875rem;
  --smaller-font-size: 0.75rem;
  
  /* Font Weight */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semi-bold: 600;
  --font-bold: 700;
  --font-extra-bold: 800;
  
  /* Spacing */
  --mb-0-25: 0.25rem;
  --mb-0-5: 0.5rem;
  --mb-0-75: 0.75rem;
  --mb-1: 1rem;
  --mb-1-25: 1.25rem;
  --mb-1-5: 1.5rem;
  --mb-2: 2rem;
  --mb-2-5: 2.5rem;
  --mb-3: 3rem;
  
  /* Z-index */
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;
  
  /* Transitions */
  --transition: all 0.3s ease;
  --transition-fast: all 0.2s ease;
  
  /* Border Radius */
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --text-color: #f9fafb;
  --text-color-light: #d1d5db;
  --text-color-lighter: #9ca3af;
  --body-color: #111827;
  --container-color: #1f2937;
  --card-color: #374151;
  --border-color: #4b5563;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--body-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: var(--transition);
}

h1, h2, h3, h4 {
  color: var(--text-color);
  font-weight: var(--font-semi-bold);
  line-height: 1.2;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  font-family: inherit;
}

/* ===== REUSABLE CSS CLASSES ===== */
.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  padding: 4rem 0 2rem;
}

.section__title {
  font-size: var(--h2-font-size);
  color: var(--text-color);
  text-align: center;
  margin-bottom: var(--mb-0-75);
}

.section__subtitle {
  display: block;
  font-size: var(--small-font-size);
  color: var(--text-color-light);
  text-align: center;
  margin-bottom: var(--mb-2-5);
}

.section__header {
  margin-bottom: var(--mb-3);
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--mb-0-5);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-medium);
  font-size: var(--normal-font-size);
  transition: var(--transition);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  background-color: var(--body-color);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.nav {
  height: 4rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
  font-weight: var(--font-bold);
  font-size: 1.25rem;
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
}

.nav__logo:hover {
  transform: scale(1.05);
}

.nav__logo i {
  font-size: 1.5rem;
}

.nav__list {
  display: flex;
  gap: var(--mb-2);
}

.nav__link {
  display: flex;
  align-items: center;
  gap: var(--mb-0-25);
  color: var(--text-color);
  font-weight: var(--font-medium);
  transition: var(--transition);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
}

.nav__link:hover,
.nav__link.active-link {
  color: var(--primary-color);
  background-color: var(--container-color);
}

.nav__actions {
  display: flex;
  align-items: center;
  gap: var(--mb-1);
}

/* Search Container */
.search-container {
  position: relative;
}

.search-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.search-toggle:hover {
  background-color: var(--container-color);
  color: var(--primary-color);
}

.search-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 300px;
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: 1rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
  z-index: var(--z-tooltip);
}

.search-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--normal-font-size);
  background: var(--body-color);
  color: var(--text-color);
  margin-bottom: var(--mb-1);
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.25rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--body-color);
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  transition: var(--transition);
}

.filter-btn.active,
.filter-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Theme Toggle */
.theme-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.theme-toggle:hover {
  background-color: var(--container-color);
  color: var(--primary-color);
}

/* Language Switcher */
.language-switcher {
  position: relative;
}

.lang-toggle {
  display: flex;
  align-items: center;
  gap: var(--mb-0-25);
  background: none;
  border: none;
  color: var(--text-color);
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.lang-toggle:hover {
  background-color: var(--container-color);
  color: var(--primary-color);
}

.lang-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
  z-index: var(--z-tooltip);
  min-width: 120px;
}

.lang-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: block;
  width: 100%;
  padding: 0.5rem;
  background: none;
  border: none;
  color: var(--text-color);
  font-size: var(--small-font-size);
  text-align: left;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.lang-option:hover,
.lang-option.active {
  background-color: var(--primary-color);
  color: white;
}

.user-actions {
  display: flex;
  gap: var(--mb-0-5);
}

/* Mobile Navigation */
.nav__toggle,
.nav__close {
  display: none;
  font-size: 1.25rem;
  color: var(--text-color);
  cursor: pointer;
}

/* ===== HERO SECTION ===== */
.hero {
  padding-top: 6rem;
  background: var(--gradient-hero);
  color: white;
  overflow: hidden;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero__container {
  position: relative;
  z-index: 2;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--mb-3);
  align-items: center;
  margin-bottom: var(--mb-3);
}

.hero__title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-extra-bold);
  margin-bottom: var(--mb-1);
  line-height: 1.1;
}

.hero__description {
  font-size: 1.1rem;
  margin-bottom: var(--mb-2);
  opacity: 0.9;
  line-height: 1.6;
}

.hero__buttons {
  display: flex;
  gap: var(--mb-1);
  flex-wrap: wrap;
}

.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__product-showcase {
  position: relative;
}

.product-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-xl);
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition);
  animation: float 6s ease-in-out infinite;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.product-image {
  font-size: 3rem;
  margin-bottom: var(--mb-1);
  color: var(--secondary-color);
}

.product-info h3 {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-5);
}

.price {
  font-size: 1.25rem;
  font-weight: var(--font-bold);
  color: var(--secondary-color);
  margin-bottom: var(--mb-0-5);
}

.rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.rating i {
  color: var(--secondary-color);
}

/* Category Navigation */
.category-nav {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--mb-1);
  margin-top: var(--mb-2);
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--mb-0-5);
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-lg);
  transition: var(--transition);
  cursor: pointer;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
}

.category-item i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.category-item span {
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  text-align: center;
}

/* ===== BACK TO TOP ===== */
.back-to-top {
  position: fixed;
  right: 1rem;
  bottom: 1rem;
  background: var(--primary-color);
  color: white;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: var(--z-tooltip);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background: var(--primary-color-alt);
  transform: translateY(-3px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 768px) {
  :root {
    --h1-font-size: 2rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.25rem;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .nav__menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background-color: var(--body-color);
    box-shadow: var(--shadow-xl);
    padding: 4rem 2rem 2rem;
    transition: var(--transition);
  }

  .nav__menu.show-menu {
    right: 0;
  }

  .nav__list {
    flex-direction: column;
    gap: var(--mb-1-5);
  }

  .nav__close {
    display: block;
    position: absolute;
    top: 1rem;
    right: 1rem;
  }

  .nav__toggle {
    display: block;
  }

  .nav__actions .user-actions {
    display: none;
  }

  .hero__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero__buttons {
    justify-content: center;
  }

  .category-nav {
    grid-template-columns: repeat(2, 1fr);
  }

  .search-dropdown {
    width: 280px;
  }
}

/* ===== VOUCHERS SECTION ===== */
.vouchers {
  background-color: var(--container-color);
}

.vouchers__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--mb-2);
}

.voucher-card {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.voucher-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.voucher-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--mb-1);
}

.voucher-business {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
}

.business-logo {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.business-name {
  font-weight: var(--font-semi-bold);
  color: var(--text-color);
}

.voucher-discount {
  background: var(--gradient-secondary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-bold);
  font-size: var(--small-font-size);
}

.voucher-title {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: var(--mb-0-5);
}

.voucher-description {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
  line-height: 1.5;
}

.voucher-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--mb-1);
}

.savings-amount {
  color: var(--success-color);
  font-weight: var(--font-bold);
  font-size: var(--small-font-size);
}

.voucher-expiry {
  color: var(--warning-color);
  font-size: var(--smaller-font-size);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.voucher-info-icons {
  display: flex;
  gap: var(--mb-0-5);
  margin-bottom: var(--mb-1);
}

.info-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--container-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-light);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.info-icon:hover {
  background: var(--primary-color);
  color: white;
}

.info-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-color);
  color: white;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: var(--z-tooltip);
}

.info-icon:hover .info-tooltip {
  opacity: 1;
  visibility: visible;
}

.voucher-actions {
  display: flex;
  gap: var(--mb-0-5);
}

.btn-voucher {
  flex: 1;
  padding: 0.75rem;
  font-size: var(--small-font-size);
  justify-content: center;
}

/* ===== TRENDING PRODUCTS SECTION ===== */
.products__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--mb-2);
}

.product-card-main {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.product-card-main:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.product-image-main {
  height: 200px;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  position: relative;
  overflow: hidden;
}

.product-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--secondary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-bold);
}

.product-content {
  padding: 1.5rem;
}

.product-title {
  font-size: var(--h3-font-size);
  color: var(--text-color);
  margin-bottom: var(--mb-0-5);
}

.product-description {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
  line-height: 1.5;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--mb-1);
}

.product-price {
  font-size: 1.25rem;
  font-weight: var(--font-bold);
  color: var(--primary-color);
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--secondary-color);
  font-size: var(--small-font-size);
}

.product-seller {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
  margin-bottom: var(--mb-1);
  padding: 0.5rem;
  background: var(--container-color);
  border-radius: var(--border-radius);
}

.seller-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--small-font-size);
}

.seller-info {
  flex: 1;
}

.seller-name {
  font-weight: var(--font-medium);
  color: var(--text-color);
  font-size: var(--small-font-size);
}

.seller-rating {
  color: var(--text-color-light);
  font-size: var(--smaller-font-size);
}

.product-actions {
  display: flex;
  gap: var(--mb-0-5);
}

.btn-product {
  flex: 1;
  padding: 0.75rem;
  font-size: var(--small-font-size);
  justify-content: center;
}

/* ===== ABOUT SECTION ===== */
.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--mb-3);
  align-items: center;
}

.about__text .section__title {
  text-align: left;
  margin-bottom: var(--mb-1);
}

.about__text p {
  color: var(--text-color-light);
  margin-bottom: var(--mb-2);
  line-height: 1.6;
}

.about__features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--mb-1);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--mb-0-5);
  padding: 1rem;
  background: var(--container-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.feature:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.feature i {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.feature:hover i {
  color: white;
}

.feature span {
  font-weight: var(--font-medium);
  font-size: var(--small-font-size);
}

.about__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.about__stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--mb-1-5);
}

.stat {
  text-align: center;
  padding: 2rem 1rem;
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.stat:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.stat h3 {
  font-size: 2rem;
  font-weight: var(--font-extra-bold);
  color: var(--primary-color);
  margin-bottom: var(--mb-0-5);
}

.stat p {
  color: var(--text-color-light);
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-on-scroll {
  opacity: 0;
  transition: var(--transition);
}

.animate-on-scroll.animate {
  animation: fadeInUp 0.8s ease forwards;
}

.animate-left.animate {
  animation: fadeInLeft 0.8s ease forwards;
}

.animate-right.animate {
  animation: fadeInRight 0.8s ease forwards;
}

/* ===== RESPONSIVE DESIGN ADDITIONS ===== */
@media screen and (max-width: 768px) {
  .vouchers__grid {
    grid-template-columns: 1fr;
  }

  .products__grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .about__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .about__features {
    grid-template-columns: 1fr;
  }

  .about__stats {
    grid-template-columns: 1fr;
  }

  .voucher-actions {
    flex-direction: column;
  }

  .product-actions {
    flex-direction: column;
  }
}
