/*==================== MYBIZ PLATFORM - CORE JAVASCRIPT ====================*/

// Global Variables
let currentLanguage = localStorage.getItem('mybiz-language') || 'en';
let currentTheme = localStorage.getItem('mybiz-theme') || 'light';

// DOM Elements
const navMenu = document.getElementById('nav-menu');
const navToggle = document.getElementById('nav-toggle');
const navClose = document.getElementById('nav-close');
const themeToggle = document.getElementById('theme-toggle');
const langToggle = document.getElementById('lang-toggle');
const langDropdown = document.getElementById('lang-dropdown');
const searchToggle = document.getElementById('search-toggle');
const searchDropdown = document.getElementById('search-dropdown');
const backToTop = document.getElementById('back-to-top');

/*==================== NAVIGATION MENU ====================*/
// Show menu
if (navToggle) {
    navToggle.addEventListener('click', () => {
        navMenu.classList.add('show-menu');
    });
}

// Hide menu
if (navClose) {
    navClose.addEventListener('click', () => {
        navMenu.classList.remove('show-menu');
    });
}

// Remove menu mobile
const navLinks = document.querySelectorAll('.nav__link');
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('show-menu');
    });
});

/*==================== THEME SWITCHER ====================*/
function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('mybiz-theme', currentTheme);
    updateThemeIcon();
}

function updateThemeIcon() {
    const themeIcon = themeToggle.querySelector('i');
    if (currentTheme === 'dark') {
        themeIcon.className = 'fas fa-sun';
    } else {
        themeIcon.className = 'fas fa-moon';
    }
}

if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
}

/*==================== LANGUAGE SWITCHER ====================*/
const translations = {
    en: {
        'Home': 'Home',
        'Marketplace': 'Marketplace',
        'All Shops': 'All Shops',
        'Jobs': 'Jobs',
        'Login': 'Login',
        'Sign Up': 'Sign Up',
        "Tanzania's Premier": "Tanzania's Premier",
        'Multi-Tenant E-Commerce Platform': 'Multi-Tenant E-Commerce Platform',
        'Connect local businesses with customers through our comprehensive marketplace, business directory, and job portal. Empowering Tanzanian entrepreneurs to grow their digital presence.': 'Connect local businesses with customers through our comprehensive marketplace, business directory, and job portal. Empowering Tanzanian entrepreneurs to grow their digital presence.',
        'Start Your Business': 'Start Your Business',
        'Watch Demo': 'Watch Demo',
        'Electronics': 'Electronics',
        'Fashion': 'Fashion',
        'Food & Drinks': 'Food & Drinks',
        'Services': 'Services',
        'Health & Beauty': 'Health & Beauty',
        'More': 'More',
        'Featured Vouchers': 'Featured Vouchers',
        'Save money with exclusive deals from our partner businesses': 'Save money with exclusive deals from our partner businesses',
        'Trending Products': 'Trending Products',
        'Discover the most popular products from our partner stores': 'Discover the most popular products from our partner stores',
        'About MyBiz': 'About MyBiz',
        'MyBiz is Tanzania\'s premier multi-tenant e-commerce platform designed to empower local businesses and connect them with customers nationwide. Our comprehensive solution includes a marketplace, business directory, job portal, and voucher system.': 'MyBiz is Tanzania\'s premier multi-tenant e-commerce platform designed to empower local businesses and connect them with customers nationwide. Our comprehensive solution includes a marketplace, business directory, job portal, and voucher system.',
        'Multi-Tenant Marketplace': 'Multi-Tenant Marketplace',
        'Business Directory': 'Business Directory',
        'Job Portal': 'Job Portal',
        'Voucher System': 'Voucher System',
        'Partner Businesses': 'Partner Businesses',
        'Active Users': 'Active Users',
        'Job Opportunities': 'Job Opportunities'
    },
    sw: {
        'Home': 'Nyumbani',
        'Marketplace': 'Soko',
        'All Shops': 'Maduka Yote',
        'Jobs': 'Kazi',
        'Login': 'Ingia',
        'Sign Up': 'Jisajili',
        "Tanzania's Premier": "Jukwaa Kuu la",
        'Multi-Tenant E-Commerce Platform': 'Jukwaa la Biashara',
        'Connect local businesses with customers through our comprehensive marketplace, business directory, and job portal. Empowering Tanzanian entrepreneurs to grow their digital presence.': 'Unganisha biashara za mitaa na wateja kupitia soko letu kamili, saraka ya biashara, na jukwaa la ajira. Tunawawezesha wafanyabiashara wa Tanzania kukuza uwepo wao wa kidijitali.',
        'Start Your Business': 'Anza Biashara Yako',
        'Watch Demo': 'Ona Mfano',
        'Electronics': 'Vifaa vya Umeme',
        'Fashion': 'Mavazi',
        'Food & Drinks': 'Chakula na Vinywaji',
        'Services': 'Huduma',
        'Health & Beauty': 'Afya na Urembo',
        'More': 'Zaidi',
        'Featured Vouchers': 'Vocha Maalum',
        'Save money with exclusive deals from our partner businesses': 'Okoa pesa kwa matoleo maalum kutoka kwa washirika wetu',
        'Trending Products': 'Bidhaa Zinazovuma',
        'Discover the most popular products from our partner stores': 'Gundua bidhaa maarufu zaidi kutoka maduka ya washirika wetu',
        'About MyBiz': 'Kuhusu MyBiz',
        'MyBiz is Tanzania\'s premier multi-tenant e-commerce platform designed to empower local businesses and connect them with customers nationwide. Our comprehensive solution includes a marketplace, business directory, job portal, and voucher system.': 'MyBiz ni jukwaa kuu la biashara za mtandao nchini Tanzania lililobuniwa kuwawezesha biashara za mitaa na kuwaongoza na wateja kote nchini. Suluhisho letu kamili linajumuisha soko, saraka ya biashara, jukwaa la ajira, na mfumo wa vocha.',
        'Multi-Tenant Marketplace': 'Soko la Washirika Wengi',
        'Business Directory': 'Saraka ya Biashara',
        'Job Portal': 'Jukwaa la Ajira',
        'Voucher System': 'Mfumo wa Vocha',
        'Partner Businesses': 'Biashara Washirika',
        'Active Users': 'Watumiaji Hai',
        'Job Opportunities': 'Fursa za Kazi'
    }
};

function initializeLanguage() {
    updateLanguageDisplay();
    translatePage();
}

function toggleLanguage() {
    const langOptions = document.querySelectorAll('.lang-option');
    langDropdown.classList.toggle('active');
    
    langOptions.forEach(option => {
        option.addEventListener('click', () => {
            const selectedLang = option.getAttribute('data-lang');
            if (selectedLang !== currentLanguage) {
                currentLanguage = selectedLang;
                localStorage.setItem('mybiz-language', currentLanguage);
                updateLanguageDisplay();
                translatePage();
                langDropdown.classList.remove('active');
            }
        });
    });
}

function updateLanguageDisplay() {
    const langText = document.querySelector('.lang-text');
    const langOptions = document.querySelectorAll('.lang-option');
    
    if (langText) {
        langText.textContent = currentLanguage.toUpperCase();
    }
    
    langOptions.forEach(option => {
        option.classList.remove('active');
        if (option.getAttribute('data-lang') === currentLanguage) {
            option.classList.add('active');
        }
    });
}

function translatePage() {
    const elements = document.querySelectorAll('[data-en]');
    elements.forEach(element => {
        const englishText = element.getAttribute('data-en');
        const swahiliText = element.getAttribute('data-sw');
        
        if (currentLanguage === 'sw' && swahiliText) {
            element.textContent = swahiliText;
        } else if (englishText) {
            element.textContent = englishText;
        }
    });
}

if (langToggle) {
    langToggle.addEventListener('click', toggleLanguage);
}

// Close language dropdown when clicking outside
document.addEventListener('click', (e) => {
    if (!langToggle.contains(e.target)) {
        langDropdown.classList.remove('active');
    }
});

/*==================== SEARCH FUNCTIONALITY ====================*/
function toggleSearch() {
    searchDropdown.classList.toggle('active');
}

function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    // Filter button functionality
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            const category = btn.getAttribute('data-category');
            // Here you would implement the actual search filtering logic
            console.log('Filtering by category:', category);
        });
    });
    
    // Search input functionality
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            // Here you would implement the actual search logic
            console.log('Searching for:', query);
        });
    }
}

if (searchToggle) {
    searchToggle.addEventListener('click', toggleSearch);
}

// Close search dropdown when clicking outside
document.addEventListener('click', (e) => {
    if (!searchToggle.contains(e.target) && !searchDropdown.contains(e.target)) {
        searchDropdown.classList.remove('active');
    }
});

/*==================== SCROLL ANIMATIONS ====================*/
function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

/*==================== BACK TO TOP BUTTON ====================*/
function handleBackToTop() {
    if (window.scrollY >= 560) {
        backToTop.classList.add('show');
    } else {
        backToTop.classList.remove('show');
    }
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

if (backToTop) {
    backToTop.addEventListener('click', scrollToTop);
}

window.addEventListener('scroll', handleBackToTop);

/*==================== ACTIVE LINK HIGHLIGHTING ====================*/
function highlightActiveSection() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav__link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        
        if (window.scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active-link');
        if (link.getAttribute('href').includes(current)) {
            link.classList.add('active-link');
        }
    });
}

window.addEventListener('scroll', highlightActiveSection);

/*==================== LOGO CLICK TO TOP ====================*/
function initializeLogoClick() {
    const logo = document.querySelector('.nav__logo');
    if (logo) {
        logo.addEventListener('click', scrollToTop);
    }
}

/*==================== INITIALIZATION ====================*/
document.addEventListener('DOMContentLoaded', () => {
    initializeTheme();
    initializeLanguage();
    initializeSearch();
    initializeScrollAnimations();
    initializeLogoClick();
    
    // Add scroll animation classes to elements
    const sections = document.querySelectorAll('section');
    sections.forEach((section, index) => {
        section.classList.add('animate-on-scroll');
        if (index % 2 === 0) {
            section.classList.add('animate-left');
        } else {
            section.classList.add('animate-right');
        }
    });
    
    console.log('MyBiz Platform initialized successfully!');
});
